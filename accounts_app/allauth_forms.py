# --- Django Imports ---
from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

# --- Third-Party Imports ---
from allauth.account.forms import LoginForm
from allauth.socialaccount.forms import SignupForm as SocialSignupForm

# --- Local App Imports ---
from .fields import PhoneN<PERSON>berField
from .forms.common import AccessibleFormMixin
from .models import CustomUser, CustomerProfile, ServiceProviderProfile
from .validators import normalize_phone


class CozyWishSignupForm(AccessibleFormMixin, forms.Form):
    """
    Custom allauth signup form with role selection and business fields.

    Features:
    - Role selection (customer/service_provider)
    - All fields from existing CustomerSignupForm
    - Business-specific fields for service providers
    - Maintains existing validation logic
    - Handles profile creation in save method
    """

    # Email field (required by allauth)
    email = forms.EmailField(
        label=_('Email Address'),
        max_length=254,
        widget=forms.EmailInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email',
            }
        ),
        help_text=_('We\'ll never share your email with anyone else.')
    )

    # Password fields (required by allauth)
    password1 = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your password'),
                'autocomplete': 'new-password',
            }
        )
    )

    password2 = forms.CharField(
        label=_('Confirm Password'),
        widget=forms.PasswordInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Confirm your password'),
                'autocomplete': 'new-password',
            }
        )
    )

    # Role selection field
    role = forms.ChoiceField(
        label=_('Account Type'),
        choices=[
            (CustomUser.CUSTOMER, _('Customer - Book venues and services')),
            (CustomUser.SERVICE_PROVIDER, _('Service Provider - Offer venues and services')),
        ],
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
        initial=CustomUser.CUSTOMER,
        help_text=_('Choose your account type. You can change this later.')
    )

    # Terms agreement field
    agree_to_terms = forms.BooleanField(
        label=_('I agree to the Terms of Service and Privacy Policy'),
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
            }
        ),
        error_messages={
            'required': _('You must agree to the Terms of Service and Privacy Policy to create an account.'),
        }
    )
    
    # Business fields for service providers (optional initially)
    business_name = forms.CharField(
        label=_('Business Name'),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter your business name'),
            }
        ),
        help_text=_('Required for service providers')
    )
    
    contact_name = forms.CharField(
        label=_('Contact Name'),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter contact person name'),
            }
        ),
        help_text=_('Primary contact person for your business')
    )
    
    phone = PhoneNumberField(
        label=_('Phone Number'),
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter phone number'),
                'type': 'tel',
            }
        ),
        help_text=_('Business phone number (required for service providers)')
    )
    
    address = forms.CharField(
        label=_('Business Address'),
        max_length=255,
        required=False,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter business address'),
            }
        ),
        help_text=_('Street address of your business')
    )
    
    city = forms.CharField(
        label=_('City'),
        max_length=100,
        required=False,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter city'),
            }
        )
    )
    
    state = forms.ChoiceField(
        label=_('State'),
        choices=[('', _('Select State'))] + list(ServiceProviderProfile.STATE_CHOICES),
        required=False,
        widget=forms.Select(attrs={'class': 'form-select'})
    )
    
    zip_code = forms.CharField(
        label=_('ZIP Code'),
        max_length=10,
        required=False,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('Enter ZIP code'),
            }
        )
    )
    
    ein = forms.CharField(
        label=_('EIN (Tax ID)'),
        max_length=20,
        required=False,
        widget=forms.TextInput(
            attrs={
                'class': 'form-control',
                'placeholder': _('XX-XXXXXXX (optional)'),
            }
        ),
        help_text=_('Employer Identification Number (optional)')
    )

    def __init__(self, *args, **kwargs):
        """Initialize form with proper field ordering and styling."""
        super().__init__(*args, **kwargs)

    def clean_email(self):
        """
        Ensure the email address is unique.

        Raises:
            ValidationError: If the email already exists.

        Returns:
            str: The cleaned email address.
        """
        email = self.cleaned_data.get('email')
        if email and CustomUser.objects.filter(email=email).exists():
            raise ValidationError(
                _('A user with this email already exists.'),
                code='email_exists'
            )
        return email

    def clean_password2(self):
        """
        Validate that the two password entries match.

        Returns:
            str: The cleaned password.
        """
        password1 = self.cleaned_data.get('password1')
        password2 = self.cleaned_data.get('password2')

        if password1 and password2 and password1 != password2:
            raise ValidationError(
                _('The two password fields didn\'t match.'),
                code='password_mismatch'
            )

        # Validate password strength
        if password2:
            from django.contrib.auth.password_validation import validate_password
            try:
                validate_password(password2)
            except ValidationError as e:
                raise ValidationError(e.messages)

        return password2

    def clean_phone(self):
        """
        Validate and normalize phone number.

        Returns:
            str: The cleaned and normalized phone number.
        """
        phone = self.cleaned_data.get('phone')
        if phone:
            try:
                return normalize_phone(phone)
            except ValidationError:
                raise ValidationError(
                    _('Please enter a valid phone number.'),
                    code='invalid_phone'
                )
        return phone

    def clean(self):
        """
        Validate form data based on selected role.

        Returns:
            dict: Cleaned form data.
        """
        cleaned_data = super().clean()
        role = cleaned_data.get('role')

        # Validate required fields for service providers
        if role == CustomUser.SERVICE_PROVIDER:
            required_fields = ['business_name', 'contact_name', 'phone', 'address', 'city', 'state', 'zip_code']
            for field_name in required_fields:
                if not cleaned_data.get(field_name):
                    self.add_error(field_name, _('This field is required for service providers.'))

        return cleaned_data

    def save(self, request):
        """
        Save user and create appropriate profile.

        Args:
            request: HTTP request object

        Returns:
            CustomUser: The created user instance
        """
        # Create the user
        email = self.cleaned_data['email']
        password = self.cleaned_data['password1']
        role = self.cleaned_data.get('role', CustomUser.CUSTOMER)

        # Create user instance
        user = CustomUser.objects.create_user(
            email=email,
            password=password,
            role=role
        )

        # Store the role and form data for the adapter to access
        self.role = role
        self.user = user

        return user


class CozyWishSocialSignupForm(AccessibleFormMixin, forms.Form):
    """
    Custom allauth social signup form with role selection.

    Features:
    - Role selection for social signups
    - Handle profile creation for social users
    - Maintain existing validation and styling
    """

    # Role selection field
    role = forms.ChoiceField(
        label=_('Account Type'),
        choices=[
            (CustomUser.CUSTOMER, _('Customer - Book venues and services')),
            (CustomUser.SERVICE_PROVIDER, _('Service Provider - Offer venues and services')),
        ],
        widget=forms.RadioSelect(attrs={'class': 'form-check-input'}),
        initial=CustomUser.CUSTOMER,
        help_text=_('Choose your account type. You can change this later.')
    )

    # Terms agreement field
    agree_to_terms = forms.BooleanField(
        label=_('I agree to the Terms of Service and Privacy Policy'),
        required=True,
        widget=forms.CheckboxInput(
            attrs={
                'class': 'form-check-input',
            }
        ),
        error_messages={
            'required': _('You must agree to the Terms of Service and Privacy Policy to create an account.'),
        }
    )

    def __init__(self, *args, **kwargs):
        """Initialize form with proper styling."""
        # Extract sociallogin parameter if provided (required by allauth)
        self.sociallogin = kwargs.pop('sociallogin', None)
        super().__init__(*args, **kwargs)

    def save(self, request):
        """
        Save user from social signup.

        Args:
            request: HTTP request object

        Returns:
            CustomUser: The created user instance
        """
        # Get user from sociallogin
        if self.sociallogin:
            user = self.sociallogin.user
            role = self.cleaned_data.get('role', CustomUser.CUSTOMER)
            user.role = role
            user.save()
        else:
            # Fallback: create a new user
            user = CustomUser.objects.create_user(
                email=f'social_user_{timezone.now().timestamp()}@example.com',
                role=self.cleaned_data.get('role', CustomUser.CUSTOMER)
            )

        # Store the role for the adapter to access
        self.role = self.cleaned_data.get('role', CustomUser.CUSTOMER)
        self.user = user

        return user


class CozyWishLoginForm(LoginForm, AccessibleFormMixin):
    """
    Custom allauth login form with role-based access control.

    Features:
    - Maintain existing login validation
    - Keep role-based access control
    - Preserve existing styling and accessibility
    """

    def __init__(self, *args, **kwargs):
        """Initialize form with proper styling."""
        super().__init__(*args, **kwargs)

        # Remove username field if it exists (since our CustomUser doesn't use username)
        if 'username' in self.fields:
            del self.fields['username']

        # Style the login field (email)
        if 'login' in self.fields:
            self.fields['login'].widget.attrs.update({
                'class': 'form-control',
                'placeholder': _('Enter your email address'),
                'autocomplete': 'email',
            })
            self.fields['login'].label = _('Email Address')

        # Style the password field
        if 'password' in self.fields:
            self.fields['password'].widget.attrs.update({
                'class': 'form-control',
                'placeholder': _('Enter your password'),
                'autocomplete': 'current-password',
            })

    def clean(self):
        """
        Validate login credentials with role-based checks.

        Returns:
            dict: Cleaned form data.
        """
        cleaned_data = super().clean()

        # Get the authenticated user from allauth's validation
        if hasattr(self, 'user') and self.user:
            user = self.user

            # Check if user is active
            if not user.is_active:
                if user.is_service_provider:
                    raise ValidationError(
                        _('Please verify your email to activate your account.'),
                        code='inactive'
                    )
                else:
                    raise ValidationError(
                        _('Your account has been deactivated.'),
                        code='inactive'
                    )

        return cleaned_data
